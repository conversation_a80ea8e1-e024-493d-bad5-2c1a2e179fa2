#include <iostream>
#include <unordered_map>
#include <map>
#include <fstream>
#include <rpcndr.h>
#include "enum.h"
#include "globalFile.h"
#include "Identity.h"
#include "student.h"
#include "teacher.h"
#include "manager.h"

using namespace std;

void login(UserType userType) {

	Identity* person = NULL; // 使用基类指针
	int id;
	string name;
	string pwd;

	ifstream ifs;

	if(userType == USER_STUDENT){
		cout << "please input student ID:";
		cin >> id;
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		person = new Student(id,name,pwd);
		cout << "File " << STUDENT_FILE << " open success!" << endl;
		ifs.open(STUDENT_FILE, ios::in);
	}else if(userType == USER_TEACHER){
		cout << "please input teacher ID:";
		cin >> id;
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		person = new Teacher(id,name,pwd);
		ifs.open(STUDENT_FILE, ios::in);
	}else if(userType == USER_MANAGER){
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		person = new Manager(name,pwd);
		ifs.open(STUDENT_FILE, ios::in);
	}
	
	person->operMenu(); // 调用操作菜单函数
}


// 方案1：使用 unordered_map 存储完整的用户信息 - O(1) 查找效率
unordered_map<int, UserTypeInfo> userTypeMap = {
	{USER_STUDENT, {"Student", STUDENT_FILE}},
	{USER_TEACHER, {"Teacher", TEACHER_FILE}},
	{USER_MANAGER, {"Manager", ADMIN_FILE}}
};

int main() {
	int select = 0;

	while (true) {
		system("cls");
		cout << "==========================" << endl;
		// 遍历显示所有用户类型选项
		// 旧写法
		/*for (const auto& pair : userTypeMap) {
			cout << pair.first << "." << pair.second.label << endl;
		}*/
		// 新写法 C++20 结构化绑定
		for (const auto& [key, value] : userTypeMap) {
			cout << key << "." << value.label << endl;
		}
		cout << "0.exit" << endl;
		cout << "==========================" << endl;
		cout << "please input：";
		cin >> select;

		// O(1) 时间复杂度查找
		auto it = userTypeMap.find(select);
		if (it != userTypeMap.end()) {
			cout << "you select " << it->second.label << endl;
			cout << "file path: " << it->second.fileName << endl;
			login(static_cast<UserType>(select));
			cout << "Press any key to continue..." << endl;
			system("pause");
		}
		else if (select == 0) {
			exit(0);
		}
		else {
			cout << "input error, please input again!" << endl;
			cout << "Press any key to continue..." << endl;
			system("pause");
		}
	}
	return 0;
}